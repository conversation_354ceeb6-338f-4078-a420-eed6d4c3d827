// pages/history/index.js
// 引入API工具
const api = require('../../utils/api');
// 引入路由工具
const router = require('../../utils/router');

Page({

    /**
     * 页面的初始数据
     */
    data: {
        navbarHeight: 88, // 使用统一的导航栏高度
        filterIndicatorHeight: 44, // 筛选指示器高度
        games: [],
        initialScore: 0, // 初始分数
        isLoading: false,
        page: 1,
        size: 10,
        hasMore: true,
        total: 0,
        // 筛选条件
        filterType: '',
        startDate: '',
        endDate: '',
        showFilter: false,
        // 懒加载相关
        lastScrollTime: 0,
        scrollVelocity: 0
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log('历史记录页面加载');

        // 获取导航栏高度 - 与其他页面保持一致
        const systemInfo = wx.getSystemInfoSync();
        let navBarHeight = 88; // 默认高度

        if (systemInfo.statusBarHeight) {
            navBarHeight = systemInfo.statusBarHeight + 44; // 状态栏 + 导航栏
        }

        this.setData({
            navbarHeight: navBarHeight
        });
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        // 每次显示页面时重置并加载游戏列表
        this.setData({
            page: 1,
            games: [],
            hasMore: true
        });
        this.loadGames();
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {
        // 清理定时器，避免内存泄漏
        if (this.loadingTimeout) {
            clearTimeout(this.loadingTimeout);
            this.loadingTimeout = null;
        }
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        // 下拉刷新
        this.setData({
            page: 1,
            games: [],
            hasMore: true
        });
        this.loadGames();
        wx.stopPullDownRefresh();
    },

    /**
     * 页面上拉触底事件的处理函数 - 优化性能
     */
    onReachBottom() {
        // 防抖处理，避免频繁触发
        if (this.loadingTimeout) {
            clearTimeout(this.loadingTimeout);
        }

        this.loadingTimeout = setTimeout(() => {
            if (this.data.hasMore && !this.data.isLoading) {
                // 检测滑动速度，如果快速滑动则加载更多数据
                const currentTime = Date.now();
                const timeDiff = currentTime - this.data.lastScrollTime;

                if (timeDiff < 500) { // 快速滑动（500ms内触发）
                    this.loadGames(50); // 一次性加载50条
                } else {
                    this.loadGames(10); // 正常加载10条
                }

                this.setData({
                    lastScrollTime: currentTime
                });
            }
        }, 100); // 100ms 防抖
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },

    loadGames(customSize = null) {
        if (this.data.isLoading || !this.data.hasMore) return;

        // 批量更新状态，减少重新渲染
        const updateData = {
            isLoading: true
        };

        // 只在首次加载或没有数据时显示加载提示
        if (this.data.games.length === 0) {
            wx.showLoading({
                title: '加载中...',
            });
        }

        this.setData(updateData);

        // 构建查询参数
        const loadSize = customSize || this.data.size;
        const params = {
            settled: true, // 获取已结束/已结算的游戏记录
            page: this.data.page,
            size: loadSize
        };

        // 添加筛选条件
        if (this.data.filterType) params.type = this.data.filterType;
        if (this.data.startDate) params.start_date = this.data.startDate;
        if (this.data.endDate) params.end_date = this.data.endDate;

        // 调用API获取已结束的游戏历史记录
        api.games.history(params).then(res => {
            // 只在首次加载时隐藏loading
            if (this.data.games.length === 0) {
                wx.hideLoading();
            }
            console.log('获取已结束游戏历史记录成功:', res);

            // 格式化游戏数据
            const newGames = res.data.list.map(game => {
                // 格式化时间
                const date = new Date(game.createTime);
                const formattedTime = `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()}`;
                return {
                    ...game,
                    timeFormatted: formattedTime
                };
            });

            // 批量更新状态，减少重新渲染次数
            const updateData = {
                games: this.data.page === 1 ? newGames : [...this.data.games, ...newGames],
                total: res.data.total,
                hasMore: this.data.page * loadSize < res.data.total,
                page: this.data.page + 1,
                isLoading: false
            };

            this.setData(updateData);
        }).catch(err => {
            wx.hideLoading();
            console.error('获取已结束游戏历史记录失败:', err);

            this.setData({
                isLoading: false
            });

            wx.showToast({
                title: '获取历史记录失败: ' + (err.message || '未知错误'),
                icon: 'none',
                duration: 2000
            });
        });
    },

    // 显示筛选面板
    showFilterPanel() {
        this.setData({
            showFilter: true
        });
    },

    // 隐藏筛选面板
    hideFilterPanel() {
        this.setData({
            showFilter: false
        });
    },

    // 选择游戏类型
    selectGameType(e) {
        const type = e.currentTarget.dataset.type;
        this.setData({
            filterType: type === this.data.filterType ? '' : type
        });
    },

    // 选择开始日期
    bindStartDateChange(e) {
        this.setData({
            startDate: e.detail.value
        });
    },

    // 选择结束日期
    bindEndDateChange(e) {
        this.setData({
            endDate: e.detail.value
        });
    },

    // 应用筛选
    applyFilter() {
        this.setData({
            page: 1,
            games: [],
            hasMore: true,
            showFilter: false
        });
        this.loadGames();
    },

    // 重置筛选
    resetFilter() {
        this.setData({
            filterType: '',
            startDate: '',
            endDate: '',
            page: 1,
            games: [],
            hasMore: true
        });
        this.loadGames();
        this.hideFilterPanel();
    },

    navigateToGame(e) {
        const gameId = e.currentTarget.dataset.id;
        wx.navigateTo({
            url: `/pages/history/gameHistory?id=${gameId}`
        }).then(() => {
            console.log('跳转到游戏历史页面成功');
        }).catch(err => {
            console.error('跳转失败:', err);
        });
    }
})